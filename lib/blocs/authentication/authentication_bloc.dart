import 'dart:async';

import 'package:bloc/bloc.dart';
import 'package:firebase_auth/firebase_auth.dart';
import 'package:meta/meta.dart';
import 'package:sluqe/services/authentication/authentication_servces.dart';

part 'authentication_event.dart';
part 'authentication_state.dart';

class AuthenticationBloc
    extends Bloc<AuthenticationEvent, AuthenticationState> {
  final AuthenticationServces? _authenticationServices;
  StreamSubscription? _authStateChangesSubscription;

  AuthenticationBloc({AuthenticationServces? authenticationServices})
    : _authenticationServices =
          authenticationServices ?? AuthenticationServces(),
      super(AuthenticationInitial()) {
    on<AuthSignIn>(_onAuthSignIn);
    on<AuthSignOut>(_onAuthSignOut);
    on<AuthStateChanged>(_onStateChanged);
    checkAuthState();
  }

  Future<void> checkAuthState() async {
    print("checking auth state");
    _authStateChangesSubscription = _authenticationServices!.authStateChanges
        .listen((user) {
          add(AuthStateChanged(user));
        });
  }

  Future<void> _onAuthSignIn(
    AuthSignIn event,
    Emitter<AuthenticationState> emit,
  ) async {
    emit(AuthLoading());
    try {
      final userCredential = await _authenticationServices!.signInWithGoogle();
      if (userCredential?.user != null) {
        emit(Authenticated(userCredential!.user!));
      } else {
        emit(Unauthenticated());
      }
    } catch (e) {
      emit(AuthError(e.toString()));
    }
  }

  Future<void> _onAuthSignOut(
    AuthSignOut event,
    Emitter<AuthenticationState> emit,
  ) async {
    emit(AuthLoading());
    try {
      await _authenticationServices!.signOut();
      emit(Unauthenticated());
    } catch (e) {
      emit(AuthError(e.toString()));
    }
  }

  Future<void> _onStateChanged(
    AuthStateChanged event,
    Emitter<AuthenticationState> emit,
  ) async {
    if (event.user != null) {
      emit(Authenticated(event.user));
    } else {
      emit(Unauthenticated());
    }
  }

  @override
  Future<void> close() {
    _authStateChangesSubscription?.cancel();
    return super.close();
  }
}
