import 'dart:async';
import 'package:flutter/foundation.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:sluqe/services/record/background_upload_service.dart';
import 'package:sluqe/services/record/storage_service.dart';
import 'package:sluqe/services/upload_progress_service.dart';

part 'upload_queue_event.dart';
part 'upload_queue_state.dart';

class UploadQueueBloc extends Bloc<UploadQueueEvent, UploadQueueState> {
  final BackgroundUploadService _backgroundUploadService;
  final StorageService _storageService;
  final UploadProgressService _progressService;
  Timer? _statusCheckTimer;
  StreamSubscription? _progressSubscription;

  UploadQueueBloc({
    BackgroundUploadService? backgroundUploadService,
    StorageService? storageService,
  })  : _backgroundUploadService = backgroundUploadService ?? BackgroundUploadService(),
        _storageService = storageService ?? StorageService(),
        _progressService = UploadProgressService(),
        super(UploadQueueInitial()) {
    on<CheckUploadQueueStatus>(_onCheckUploadQueueStatus);
    on<StartMonitoringQueue>(_onStartMonitoringQueue);
    on<StopMonitoringQueue>(_onStopMonitoringQueue);
    on<UpdateUploadProgress>(_onUpdateUploadProgress);

    // Listen to progress updates
    _progressSubscription = _progressService.progressStream.listen((progress) {
      add(UpdateUploadProgress(progress));
    });

    // Start monitoring immediately
    add(StartMonitoringQueue());
  }

  Future<void> _onCheckUploadQueueStatus(
    CheckUploadQueueStatus event,
    Emitter<UploadQueueState> emit,
  ) async {
    try {
      // Check background upload service queue
      final backgroundQueue = await _backgroundUploadService.getQueueStatus();
      
      // Check storage service pending uploads
      final pendingUploads = await _storageService.getPendingUploads();
      
      final totalUploads = backgroundQueue.length + pendingUploads.length;
      
      if (totalUploads > 0) {
        emit(UploadQueueActive(
          queueLength: totalUploads,
          progress: event.progress,
        ));
      } else {
        emit(UploadQueueEmpty());
      }
    } catch (e) {
      emit(UploadQueueError(e.toString()));
    }
  }

  void _onStartMonitoringQueue(
    StartMonitoringQueue event,
    Emitter<UploadQueueState> emit,
  ) {
    _statusCheckTimer?.cancel();
    _statusCheckTimer = Timer.periodic(
      const Duration(seconds: 2),
      (timer) => add(CheckUploadQueueStatus()),
    );
    
    // Initial check
    add(CheckUploadQueueStatus());
  }

  void _onStopMonitoringQueue(
    StopMonitoringQueue event,
    Emitter<UploadQueueState> emit,
  ) {
    _statusCheckTimer?.cancel();
    emit(UploadQueueEmpty());
  }

  void _onUpdateUploadProgress(
    UpdateUploadProgress event,
    Emitter<UploadQueueState> emit,
  ) {
    add(CheckUploadQueueStatus(progress: event.progress));
  }

  @override
  Future<void> close() {
    _statusCheckTimer?.cancel();
    _progressSubscription?.cancel();
    return super.close();
  }
}
