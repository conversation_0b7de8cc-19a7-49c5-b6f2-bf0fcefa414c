part of 'upload_queue_bloc.dart';

@immutable
sealed class UploadQueueEvent {}

class CheckUploadQueueStatus extends UploadQueueEvent {
  final double? progress;
  
  CheckUploadQueueStatus({this.progress});
}

class StartMonitoringQueue extends UploadQueueEvent {}

class StopMonitoringQueue extends UploadQueueEvent {}

class UpdateUploadProgress extends UploadQueueEvent {
  final double progress;
  
  UpdateUploadProgress(this.progress);
}
