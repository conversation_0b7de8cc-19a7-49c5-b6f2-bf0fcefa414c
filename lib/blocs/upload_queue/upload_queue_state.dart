part of 'upload_queue_bloc.dart';

@immutable
sealed class UploadQueueState {}

class UploadQueueInitial extends UploadQueueState {}

class UploadQueueEmpty extends UploadQueueState {}

class UploadQueueActive extends UploadQueueState {
  final int queueLength;
  final double? progress;
  
  UploadQueueActive({
    required this.queueLength,
    this.progress,
  });
}

class UploadQueueError extends UploadQueueState {
  final String message;
  
  UploadQueueError(this.message);
}
