import 'dart:convert';
import 'dart:io';
import 'package:crypto/crypto.dart';
import 'package:firebase_storage/firebase_storage.dart';
import 'package:flutter/services.dart';

import 'package:http/http.dart' as http;
import 'package:path_provider/path_provider.dart';
import 'package:sluqe/models/audio.dart';

class AudioPlayerService {
  static final AudioPlayerService _instance = AudioPlayerService._internal();
  factory AudioPlayerService() => _instance;
  AudioPlayerService._internal();

  Future<String> downloadAndCacheAudio(String url) async {
    final directory = await getTemporaryDirectory();

    final bytes = utf8.encode(url);
    final digest = sha1.convert(bytes);
    const fileExtension = 'm4a';

    final fileName = '$digest.$fileExtension';
    final filePath = '${directory.path}/$fileName';
    final file = File(filePath);

    if (await file.exists()) {
      print("File found in cache: $filePath");
      return filePath;
    } else {
      print("File not in cache. Downloading from: $url");
      try {
        final response = await http.get(Uri.parse(url));
        if (response.statusCode == 200) {
          await file.writeAsBytes(response.bodyBytes);
          print("File downloaded and saved to: $filePath");
          return filePath;
        } else {
          throw Exception(
            'Failed to download file. Status code: ${response.statusCode}',
          );
        }
      } catch (e) {
        throw Exception('Error downloading file: $e');
      }
    }
  }

  Future<String> getFirebaseDownloadUrl(String storagePath) async {
    try {
      final ref = FirebaseStorage.instance.ref(storagePath);
      print("Getting download URL for: $storagePath");
      final String downloadUrl = await ref.getDownloadURL();
      print("Download URL obtained: $downloadUrl");
      return downloadUrl;
    } catch (e) {
      throw Exception('Failed to get download URL: $e');
    }
  }

  Future<String> prepareAudioFile(String storagePath) async {
    final downloadUrl = await getFirebaseDownloadUrl(storagePath);
    final localPath = await downloadAndCacheAudio(downloadUrl);
    return localPath;
  }

  List<Transcription> groupTranscriptionsByDuration(
    List<Transcription> transcriptions,
    double durationInSeconds,
  ) {
    if (transcriptions.isEmpty) return [];

    List<Transcription> grouped = [];
    double currentSectionStart = transcriptions.first.timestamp;
    String currentText = '';

    for (int i = 0; i < transcriptions.length; i++) {
      final transcript = transcriptions[i];

      if (transcript.timestamp - currentSectionStart >= durationInSeconds) {
        if (currentText.isNotEmpty) {
          grouped.add(
            Transcription(
              timestamp: currentSectionStart,
              text: ' $currentText',
            ),
          );
        }
        currentSectionStart = transcript.timestamp;
        currentText = transcript.text;
      } else {
        if (currentText.isEmpty) {
          currentText = transcript.text;
        } else {
          currentText += ' ${transcript.text}';
        }
      }
    }

    if (currentText.isNotEmpty) {
      grouped.add(
        Transcription(timestamp: currentSectionStart, text: ' $currentText'),
      );
    }

    return grouped;
  }

  List<Transcription> filterTranscriptions(
    List<Transcription> transcriptions,
    String query,
  ) {
    if (query.isEmpty) {
      return transcriptions;
    }

    return transcriptions.where((transcript) {
      return transcript.text.toLowerCase().contains(query.toLowerCase());
    }).toList();
  }

  String formatDuration(Duration duration) {
    final minutes = duration.inMinutes.remainder(60).toString().padLeft(2, '0');
    final seconds = duration.inSeconds.remainder(60).toString().padLeft(2, '0');
    return "$minutes:$seconds";
  }

  Future<String> getAssetFile(String assetPath) async {
    final byteData = await rootBundle.load(assetPath);
    final tempDir = await getTemporaryDirectory();
    final file = File('${tempDir.path}/${assetPath.split('/').last}');
    await file.writeAsBytes(byteData.buffer.asUint8List());
    return file.path;
  }

  int calculateOptimalSamples(double width) {
    return (width / 2).round();
  }
}
