import 'dart:async';
import 'package:flutter/material.dart';
import 'package:sluqe/services/record/background_upload_service.dart';

class AppLifecycleService extends WidgetsBindingObserver {
  static AppLifecycleService? _instance;
  static AppLifecycleService get instance =>
      _instance ??= AppLifecycleService._();

  AppLifecycleService._();

  final BackgroundUploadService _backgroundUploadService =
      BackgroundUploadService();
  bool _isInitialized = false;

  void initialize() {
    if (_isInitialized) return;
    WidgetsBinding.instance.addObserver(this);
    _isInitialized = true;
  }

  void dispose() {
    if (_isInitialized) {
      WidgetsBinding.instance.removeObserver(this);
      _backgroundUploadService.dispose();
      _isInitialized = false;
    }
  }

  @override
  void didChangeAppLifecycleState(AppLifecycleState state) {
    super.didChangeAppLifecycleState(state);

    switch (state) {
      case AppLifecycleState.resumed:
        _onAppResumed();
        break;
      case AppLifecycleState.paused:
        _onAppPaused();
        break;
      case AppLifecycleState.detached:
        _onAppDetached();
        break;
      case AppLifecycleState.inactive:
        break;
      case AppLifecycleState.hidden:
        break;
    }
  }

  void _onAppResumed() {
    _backgroundUploadService.resumeProcessing();
  }

  void _onAppPaused() {}

  void _onAppDetached() {
    _backgroundUploadService.dispose();
  }
}
