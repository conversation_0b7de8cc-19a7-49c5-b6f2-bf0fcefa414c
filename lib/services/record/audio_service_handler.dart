import 'dart:async';
import 'package:audio_service/audio_service.dart';
import 'package:audio_session/audio_session.dart';

class RecordingAudioHandler extends BaseAudioHandler {
  static const String _recordingId = 'recording_session';
  Timer? _updateTimer;
  Function()? _onStopCallback;

  Duration _recordingDuration = Duration.zero;
  bool _isRecording = false;

  @override
  Future<void> prepare() async {
    try {
      final session = await AudioSession.instance;
      await session.configure(
        AudioSessionConfiguration(
          avAudioSessionCategory: AVAudioSessionCategory.playAndRecord,
          avAudioSessionCategoryOptions:
              AVAudioSessionCategoryOptions.allowBluetooth |
              AVAudioSessionCategoryOptions.defaultToSpeaker,
          avAudioSessionMode: AVAudioSessionMode.defaultMode,
          avAudioSessionRouteSharingPolicy:
              AVAudioSessionRouteSharingPolicy.defaultPolicy,
          avAudioSessionSetActiveOptions: AVAudioSessionSetActiveOptions.none,
          androidAudioAttributes: const AndroidAudioAttributes(
            contentType: AndroidAudioContentType.speech,
            flags: AndroidAudioFlags.none,
            usage: AndroidAudioUsage.voiceCommunication,
          ),
          androidAudioFocusGainType: AndroidAudioFocusGainType.gain,
          androidWillPauseWhenDucked: false,
        ),
      );

      await super.prepare();
      print('AudioServiceHandler prepared successfully with audio session');
    } catch (e) {
      print('AudioServiceHandler prepare failed: $e');
    }
  }

  Future<void> startRecording({required Function() onStopPressed}) async {
    await prepare();

    try {
      final session = await AudioSession.instance;
      await session.setActive(true);
      print('AudioServiceHandler: Audio session activated');
    } catch (e) {
      print('AudioServiceHandler: Failed to activate audio session: $e');
    }

    _onStopCallback = onStopPressed;
    _isRecording = true;
    _recordingDuration = Duration.zero;

    final mediaItem = MediaItem(
      id: _recordingId,
      title: '🔴 Recording Audio',
      artist: 'Sluqe',
      album: 'Recording Session',
      duration: Duration(hours: 24), // Set a long duration for live recording
      artUri: null,
      extras: {
        'isRecording': true,
        'recordingDuration': _recordingDuration.inSeconds,
      },
    );

    this.mediaItem.add(mediaItem);
    print('AudioServiceHandler: Media item set - ${mediaItem.title}');

    final playbackStateObj = PlaybackState(
      controls: [MediaControl.stop],
      systemActions: {MediaAction.stop},
      playing: true,
      processingState: AudioProcessingState.ready,
      updatePosition: _recordingDuration,
      bufferedPosition: _recordingDuration,
      speed: 1.0,
    );

    playbackState.add(playbackStateObj);
    print(
      'AudioServiceHandler: Playback state set - playing: ${playbackStateObj.playing}',
    );

    _startDurationUpdates();

    print('AudioServiceHandler: Recording session started successfully');
  }

  void updateRecordingDuration(Duration duration) {
    if (!_isRecording) return;

    _recordingDuration = duration;

    final currentItem = mediaItem.value;
    if (currentItem != null) {
      final formattedDuration = formatDuration(duration);
      final updatedItem = currentItem.copyWith(
        title: '🔴 Recording Audio - $formattedDuration',
        extras: {
          ...currentItem.extras ?? {},
          'recordingDuration': duration.inSeconds,
        },
      );
      mediaItem.add(updatedItem);
    }

    playbackState.add(
      PlaybackState(
        controls: [MediaControl.stop],
        systemActions: {MediaAction.stop},
        playing: true,
        processingState: AudioProcessingState.ready,
        updatePosition: duration,
        bufferedPosition: duration,
        speed: 1.0,
      ),
    );
  }

  Future<void> stopRecording() async {
    _isRecording = false;
    _updateTimer?.cancel();
    _updateTimer = null;

    try {
      final session = await AudioSession.instance;
      await session.setActive(false);
      print('AudioServiceHandler: Audio session deactivated');
    } catch (e) {
      print('AudioServiceHandler: Failed to deactivate audio session: $e');
    }

    playbackState.add(
      PlaybackState(
        controls: [],
        systemActions: {},
        playing: false,
        processingState: AudioProcessingState.idle,
        updatePosition: _recordingDuration,
      ),
    );

    mediaItem.add(null);
  }

  @override
  Future<void> stop() async {
    await stopRecording();
    _onStopCallback?.call();
    await super.stop();
  }

  @override
  Future<void> pause() async {
    await stop();
  }

  void _startDurationUpdates() {
    _updateTimer?.cancel();
    _updateTimer = Timer.periodic(Duration(seconds: 1), (timer) {
      if (!_isRecording) {
        timer.cancel();
        return;
      }
    });
  }

  @override
  Future<void> onTaskRemoved() async {
    await stop();
    await super.onTaskRemoved();
  }

  @override
  Future<void> onNotificationDeleted() async {
    await stop();
    await super.onNotificationDeleted();
  }

  String formatDuration(Duration duration) {
    String twoDigits(int n) => n.toString().padLeft(2, '0');
    final hours = duration.inHours;
    final minutes = duration.inMinutes.remainder(60);
    final seconds = duration.inSeconds.remainder(60);

    if (hours > 0) {
      return '$hours:${twoDigits(minutes)}:${twoDigits(seconds)}';
    } else {
      return '${twoDigits(minutes)}:${twoDigits(seconds)}';
    }
  }

  bool get isRecording => _isRecording;

  Duration get recordingDuration => _recordingDuration;
}
