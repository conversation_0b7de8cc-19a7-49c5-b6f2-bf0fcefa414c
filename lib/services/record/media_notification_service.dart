import 'dart:async';
import 'package:sluqe/services/record/audio_service_manager.dart';

class MediaNotificationService {
  static MediaNotificationService? _instance;
  static MediaNotificationService get instance =>
      _instance ??= MediaNotificationService._();

  MediaNotificationService._();

  Future<void> initialize() async {
    print('MediaNotificationService: Initializing...');
    await AudioServiceManager.instance.initialize();
    print('MediaNotificationService: Initialization complete');
  }

  Future<void> startRecordingNotification({
    required Function() onStopPressed,
  }) async {
    final success = await AudioServiceManager.instance
        .startRecordingNotification(onStopPressed: onStopPressed);

    if (!success) {
      print('MediaNotificationService: Failed to start recording notification');
    }
  }

  void updateRecordingDuration(Duration duration) {
    AudioServiceManager.instance.updateRecordingDuration(duration);
  }

  Future<void> stopRecordingNotification() async {
    await AudioServiceManager.instance.stopRecordingNotification();
  }

  bool get isRecording => AudioServiceManager.instance.isRecording;

  Duration get recordingDuration =>
      AudioServiceManager.instance.recordingDuration;

  String formatDuration(Duration duration) {
    return AudioServiceManager.instance.formatDuration(duration);
  }

  Future<void> dispose() async {
    await AudioServiceManager.instance.stopRecordingNotification();
  }

  bool get isAvailable => AudioServiceManager.instance.isAvailable;

  bool get isInitialized => AudioServiceManager.instance.isInitialized;
}
