import 'dart:convert';
import 'dart:io';

import 'package:path/path.dart' as path;
import 'package:path_provider/path_provider.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'package:sluqe/models/folder.dart';
import 'package:sluqe/models/recording_progress.dart';

class StorageService {
  static const String _recordingPathKey = 'current_recording_path';
  static const String _durationKey = 'recording_duration';
  static const String _isActiveKey = 'is_recording_active';
  static const String _lastSaveTimeKey = 'last_save_time';
  static const String _recorderInitializedKey = 'recorder_initialized';
  static const String _failedUploadsKey = 'failed_uploads';
  static const String _selectedFolderKey = 'selected_folder';
  static const String _recordingProgressKey = 'recording_progress';
  static const String _pendingUploadsKey = 'pending_uploads';

  Future<void> saveRecordingProgress(RecordingProgress progress) async {
    final prefs = await SharedPreferences.getInstance();

    final fileName = path.basename(progress.path);

    final progressWithFileName = RecordingProgress(
      path: progress.path,
      fileName: fileName,
      duration: progress.duration,
      lastSaveTime: progress.lastSaveTime,
      isActive: progress.isActive,
      selectedFolder: progress.selectedFolder,
    );

    await prefs.setString(
      _recordingProgressKey,
      jsonEncode(progressWithFileName.toJson()),
    );
  }

  Future<RecordingProgress?> getRecordingProgress() async {
    final prefs = await SharedPreferences.getInstance();
    final progressJson = prefs.getString(_recordingProgressKey);

    if (progressJson == null) return null;

    try {
      final progressMap = jsonDecode(progressJson);
      final savedProgress = RecordingProgress.fromJson(progressMap);

      if (!savedProgress.isActive) return null;

      final directory = await getApplicationDocumentsDirectory();
      final currentAbsolutePath = path.join(
        directory.path,
        savedProgress.fileName,
      );

      final file = File(currentAbsolutePath);
      if (await file.exists()) {
        return RecordingProgress(
          path: currentAbsolutePath, // Use the reconstructed path
          fileName: savedProgress.fileName,
          duration: savedProgress.duration,
          lastSaveTime: savedProgress.lastSaveTime,
          isActive: savedProgress.isActive,
          selectedFolder: savedProgress.selectedFolder,
        );
      } else {
        print(
          'Recording file not found at reconstructed path: $currentAbsolutePath',
        );
        return null;
      }
    } catch (e) {
      print('Error parsing recording progress: $e');
      return null;
    }
  }

  Future<void> clearRecordingProgress() async {
    final prefs = await SharedPreferences.getInstance();
    await prefs.remove(_recordingProgressKey);
  }

  Future<void> saveFailedUpload(String path) async {
    final prefs = await SharedPreferences.getInstance();
    final failedUploads = prefs.getStringList(_failedUploadsKey) ?? [];
    failedUploads.add(path);
    await prefs.setStringList(_failedUploadsKey, failedUploads);
  }

  Future<List<String>> getFailedUploads() async {
    final prefs = await SharedPreferences.getInstance();
    return prefs.getStringList(_failedUploadsKey) ?? [];
  }

  Future<void> removeFailedUpload(String path) async {
    final prefs = await SharedPreferences.getInstance();
    final failedUploads = prefs.getStringList(_failedUploadsKey) ?? [];
    failedUploads.remove(path);
    await prefs.setStringList(_failedUploadsKey, failedUploads);
  }

  Future<void> clearFailedUploads() async {
    final prefs = await SharedPreferences.getInstance();
    await prefs.remove(_failedUploadsKey);
  }

  Future<void> saveUploadMetadata({
    required String filePath,
    required String userId,
    required String? folderId,
  }) async {
    final prefs = await SharedPreferences.getInstance();
    final metadata = {
      'filePath': filePath,
      'userId': userId,
      'folderId': folderId,
      'timestamp': DateTime.now().millisecondsSinceEpoch,
    };

    await prefs.setString(
      'upload_metadata_${path.basename(filePath)}',
      jsonEncode(metadata),
    );
  }

  Future<Map<String, dynamic>?> getUploadMetadata(String fileName) async {
    final prefs = await SharedPreferences.getInstance();
    final metadataJson = prefs.getString('upload_metadata_$fileName');

    if (metadataJson == null) return null;

    try {
      return jsonDecode(metadataJson);
    } catch (e) {
      return null;
    }
  }

  Future<void> removeUploadMetadata(String fileName) async {
    final prefs = await SharedPreferences.getInstance();
    await prefs.remove('upload_metadata_$fileName');
  }

  Future<void> addPendingUpload({
    required String filePath,
    required String userId,
    required Folder? folder,
  }) async {
    final prefs = await SharedPreferences.getInstance();
    final pendingUploadsJson = prefs.getString(_pendingUploadsKey) ?? '[]';
    final pendingUploads = List<Map<String, dynamic>>.from(
      jsonDecode(pendingUploadsJson),
    );

    final uploadData = {
      'filePath': filePath,
      'userId': userId,
      'folder': folder?.toMap(),
      'timestamp': DateTime.now().millisecondsSinceEpoch,
      'isUploaded': false,
      'retryCount': 0,
    };

    pendingUploads.add(uploadData);
    await prefs.setString(_pendingUploadsKey, jsonEncode(pendingUploads));
  }

  Future<void> markUploadCompleted(String filePath) async {
    final prefs = await SharedPreferences.getInstance();
    final pendingUploadsJson = prefs.getString(_pendingUploadsKey) ?? '[]';
    final pendingUploads = List<Map<String, dynamic>>.from(
      jsonDecode(pendingUploadsJson),
    );

    for (int i = 0; i < pendingUploads.length; i++) {
      if (pendingUploads[i]['filePath'] == filePath) {
        pendingUploads[i]['isUploaded'] = true;
        break;
      }
    }

    await prefs.setString(_pendingUploadsKey, jsonEncode(pendingUploads));
  }

  Future<List<Map<String, dynamic>>> getPendingUploads() async {
    final prefs = await SharedPreferences.getInstance();
    final pendingUploadsJson = prefs.getString(_pendingUploadsKey) ?? '[]';
    final pendingUploads = List<Map<String, dynamic>>.from(
      jsonDecode(pendingUploadsJson),
    );

    return pendingUploads
        .where((upload) => upload['isUploaded'] != true)
        .toList();
  }

  Future<void> removePendingUpload(String filePath) async {
    final prefs = await SharedPreferences.getInstance();
    final pendingUploadsJson = prefs.getString(_pendingUploadsKey) ?? '[]';
    final pendingUploads = List<Map<String, dynamic>>.from(
      jsonDecode(pendingUploadsJson),
    );

    pendingUploads.removeWhere((upload) => upload['filePath'] == filePath);
    await prefs.setString(_pendingUploadsKey, jsonEncode(pendingUploads));
  }

  Future<void> clearCompletedUploads() async {
    final prefs = await SharedPreferences.getInstance();
    final pendingUploadsJson = prefs.getString(_pendingUploadsKey) ?? '[]';
    final pendingUploads = List<Map<String, dynamic>>.from(
      jsonDecode(pendingUploadsJson),
    );

    final activePendingUploads =
        pendingUploads.where((upload) => upload['isUploaded'] != true).toList();
    await prefs.setString(_pendingUploadsKey, jsonEncode(activePendingUploads));
  }
}
