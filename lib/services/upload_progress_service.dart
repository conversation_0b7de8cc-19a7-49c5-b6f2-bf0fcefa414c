import 'dart:async';

class UploadProgressService {
  static final UploadProgressService _instance = UploadProgressService._internal();
  factory UploadProgressService() => _instance;
  UploadProgressService._internal();

  StreamController<double>? _progressController;
  StreamController<bool>? _uploadingController;

  Stream<double> get progressStream {
    _progressController ??= StreamController<double>.broadcast();
    return _progressController!.stream;
  }

  Stream<bool> get uploadingStream {
    _uploadingController ??= StreamController<bool>.broadcast();
    return _uploadingController!.stream;
  }

  void updateProgress(double progress) {
    _progressController ??= StreamController<double>.broadcast();
    if (!_progressController!.isClosed) {
      _progressController!.add(progress);
    }
  }

  void setUploading(bool isUploading) {
    _uploadingController ??= StreamController<bool>.broadcast();
    if (!_uploadingController!.isClosed) {
      _uploadingController!.add(isUploading);
    }
  }

  void dispose() {
    _progressController?.close();
    _uploadingController?.close();
    _progressController = null;
    _uploadingController = null;
  }
}
