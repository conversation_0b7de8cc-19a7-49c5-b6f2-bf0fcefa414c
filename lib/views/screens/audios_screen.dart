import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:sluqe/blocs/audio/audio_bloc.dart';
import 'package:sluqe/blocs/authentication/authentication_bloc.dart';
import 'package:sluqe/blocs/upload_queue/upload_queue_bloc.dart';
import 'package:sluqe/core/constants.dart';
import 'package:sluqe/models/audio.dart';
import 'package:sluqe/views/widgets/audio_card.dart';
import 'package:sluqe/views/widgets/upload_progress_card.dart';

class AudioListScreen extends StatefulWidget {
  const AudioListScreen({super.key, required this.onAudioSelected});
  final Function(Audio audio) onAudioSelected;

  @override
  State<AudioListScreen> createState() => _AudioListScreenState();
}

class _AudioListScreenState extends State<AudioListScreen> {
  final TextEditingController _searchController = TextEditingController();

  @override
  Widget build(BuildContext context) {
    final userId =
        (context.read<AuthenticationBloc>().state as Authenticated).user.uid;
    return MultiBlocProvider(
      providers: [
        BlocProvider(
          create: (context) => AudioBloc()..add(GetAllAudiosEvent(userId)),
        ),
        BlocProvider(
          create: (context) => UploadQueueBloc(),
        ),
      ],
      child: BlocBuilder<AudioBloc, AudioState>(
        builder: (context, state) {
          return Scaffold(
            backgroundColor: bgColor,
            body: Column(
              children: [
                Container(
                  padding: const EdgeInsets.fromLTRB(16, 60, 16, 16),

                  child: Container(
                    decoration: BoxDecoration(
                      border: Border.all(),

                      borderRadius: BorderRadius.circular(25),
                      boxShadow: [
                        BoxShadow(
                          color: Colors.black.withValues(alpha: 0.05),
                          blurRadius: 10,
                          offset: const Offset(0, 2),
                        ),
                      ],
                    ),
                    child: TextField(
                      controller: _searchController,
                      onChanged: (value) {
                        context.read<AudioBloc>().add(
                          SearchAudioEvent(userId, value),
                        );
                      },
                      decoration: InputDecoration(
                        hintText: 'Search Recordings',
                        hintStyle: TextStyle(color: Colors.grey, fontSize: 16),
                        prefixIcon: const Icon(Icons.menu, color: Colors.black),
                        suffixIcon: const Icon(
                          Icons.search,
                          color: Colors.black,
                        ),
                        border: InputBorder.none,
                        contentPadding: const EdgeInsets.symmetric(
                          horizontal: 20,
                          vertical: 12,
                        ),
                      ),
                    ),
                  ),
                ),

                state is AudioInitial
                    ? Column(
                        children: [
                          // Upload progress card
                          BlocBuilder<UploadQueueBloc, UploadQueueState>(
                            builder: (context, uploadState) {
                              if (uploadState is UploadQueueActive) {
                                return UploadProgressCard(
                                  progress: uploadState.progress,
                                  isUploading: true,
                                );
                              }
                              return const SizedBox.shrink();
                            },
                          ),
                          const Expanded(
                            child: Center(
                              child: Text('Press the button to load audios'),
                            ),
                          ),
                        ],
                      )
                    : state is AudioLoading
                    ? Column(
                        children: [
                          // Upload progress card
                          BlocBuilder<UploadQueueBloc, UploadQueueState>(
                            builder: (context, uploadState) {
                              if (uploadState is UploadQueueActive) {
                                return UploadProgressCard(
                                  progress: uploadState.progress,
                                  isUploading: true,
                                );
                              }
                              return const SizedBox.shrink();
                            },
                          ),
                          const Expanded(
                            child: Center(child: CircularProgressIndicator()),
                          ),
                        ],
                      )
                    : state is AudioSuccess
                    ? state.audios.isEmpty
                        ? Column(
                            children: [
                              // Upload progress card (even when no audios)
                              BlocBuilder<UploadQueueBloc, UploadQueueState>(
                                builder: (context, uploadState) {
                                  if (uploadState is UploadQueueActive) {
                                    return UploadProgressCard(
                                      progress: uploadState.progress,
                                      isUploading: true,
                                    );
                                  }
                                  return const SizedBox.shrink();
                                },
                              ),
                              const Expanded(
                                child: Center(child: Text('No audios found')),
                              ),
                            ],
                          )
                        : Flexible(
                          child: Column(
                            children: [
                              // Upload progress card
                              BlocBuilder<UploadQueueBloc, UploadQueueState>(
                                builder: (context, uploadState) {
                                  if (uploadState is UploadQueueActive) {
                                    return UploadProgressCard(
                                      progress: uploadState.progress,
                                      isUploading: true,
                                    );
                                  }
                                  return const SizedBox.shrink();
                                },
                              ),
                              // Audio list
                              Expanded(
                                child: ListView.builder(
                                  padding: EdgeInsets.zero,
                                  shrinkWrap: true,
                                  itemCount: state.audios.length,
                                  itemBuilder: (context, index) {
                                    final audio = state.audios[index];
                                    return GestureDetector(
                                      onTap: () {
                                        widget.onAudioSelected(audio)!;
                                      },
                                      child: AudioCard(audio: audio),
                                    );
                                  },
                                ),
                              ),
                            ],
                          ),
                        )
                    : state is AudioError
                    ? Column(
                        children: [
                          // Upload progress card
                          BlocBuilder<UploadQueueBloc, UploadQueueState>(
                            builder: (context, uploadState) {
                              if (uploadState is UploadQueueActive) {
                                return UploadProgressCard(
                                  progress: uploadState.progress,
                                  isUploading: true,
                                );
                              }
                              return const SizedBox.shrink();
                            },
                          ),
                          Expanded(
                            child: Center(
                              child: Column(
                                mainAxisAlignment: MainAxisAlignment.center,
                                children: [
                                  const Icon(Icons.error, size: 64, color: Colors.red),
                                  const SizedBox(height: 16),
                                  Text(
                                    'Error: ${state.message}',
                                    textAlign: TextAlign.center,
                                    style: const TextStyle(fontSize: 16),
                                  ),
                                  const SizedBox(height: 16),
                                  ElevatedButton(
                                    onPressed: () {
                                      context.read<AudioBloc>().add(
                                        GetAllAudiosEvent(userId),
                                      );
                                    },
                                    child: const Text('Retry'),
                                  ),
                                ],
                              ),
                            ),
                          ),
                        ],
                      )
                    : const SizedBox.shrink(),
              ],
            ),
          );
        },
      ),
    );
  }
}
