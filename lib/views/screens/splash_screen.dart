import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:sluqe/blocs/authentication/authentication_bloc.dart';
import 'package:sluqe/core/constants.dart';
import 'package:sluqe/views/screens/home_screen.dart';
import 'package:sluqe/views/screens/login_screen.dart';

class SplashScreen extends StatefulWidget {
  const SplashScreen({super.key});

  @override
  State<SplashScreen> createState() => _SplashScreenState();
}

class _SplashScreenState extends State<SplashScreen> {
  @override
  void initState() {
    super.initState();
  }

  @override
  Widget build(BuildContext context) {
    return BlocListener<AuthenticationBloc, AuthenticationState>(
      listener: (listenerContext, state) {
        if (state is AuthenticationInitial) {
        } else if (state is Authenticated) {
          Future.delayed(const Duration(seconds: 2), () {
            if (context.mounted) {
              Navigator.of(context).pushAndRemoveUntil(
                MaterialPageRoute(builder: (context) => const HomeScreen()),
                (route) => false,
              );
            }
          });
        } else if (state is Unauthenticated) {
          Future.delayed(const Duration(seconds: 2), () {
            if (context.mounted) {
              Navigator.of(context).pushAndRemoveUntil(
                MaterialPageRoute(builder: (context) => const LoginScreen()),
                (route) => false,
              );
            }
          });
        }
      },

      child: Scaffold(
        backgroundColor: primaryColor,
        body: Center(child: Image.asset("assets/splash_logo.png")),
      ),
    );
  }
}
