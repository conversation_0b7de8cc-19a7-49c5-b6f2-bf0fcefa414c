.DS_Store
.atom/
.idea/
.vscode/

.packages
.pub/
.dart_tool/
pubspec.lock
flutter_export_environment.sh

examples/all_plugins/pubspec.yaml

Podfile
Podfile.lock
Pods/
.symlinks/
**/Flutter/App.framework/
**/Flutter/ephemeral/
**/Flutter/Flutter.podspec
**/Flutter/Flutter.framework/
**/Flutter/Generated.xcconfig
**/Flutter/flutter_assets/

ServiceDefinitions.json
xcuserdata/
**/DerivedData/

local.properties
keystore.properties
.gradle/
gradlew
gradlew.bat
gradle-wrapper.jar
.flutter-plugins-dependencies
*.iml

generated_plugin_registrant.dart
GeneratedPluginRegistrant.h
GeneratedPluginRegistrant.m
generated_plugin_registrant.cc
GeneratedPluginRegistrant.java
GeneratedPluginRegistrant.swift
build/
.flutter-plugins

.project
.classpath
.settings
.github
