## [0.0.6+1]
* Changed constraints to support flutter 2.20.0
* Fix for missing transparency after crop (thanks to netgfx)

## [0.0.6]
* Added null-safety support (thanks to dw2kim)

## [0.0.5+3]
* v2 Embedding (thanks to adithyaxx)

## [0.0.5+2]
* Reformatted flutter_native_image.dart
* Bump version to get the analysis going again

## [0.0.5+1]

* Removed some sample code
* Added documentation for public API's

## [0.0.5]

* Updated to AndroidX
* Changed constraints to only support flutter >=1.10.0


## [0.0.1] - TODO: Add release date.

* Initial functionality
