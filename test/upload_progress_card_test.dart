import 'package:flutter/material.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:sluqe/views/widgets/upload_progress_card.dart';

void main() {
  group('UploadProgressCard Widget Tests', () {
    testWidgets('UploadProgressCard should display loading animation and text', (WidgetTester tester) async {
      await tester.pumpWidget(
        MaterialApp(
          home: Scaffold(
            body: UploadProgressCard(
              progress: 0.5,
              isUploading: true,
            ),
          ),
        ),
      );

      // Verify the upload text is displayed
      expect(find.text('Please wait for upload to complete...'), findsOneWidget);
      
      // Verify progress percentage is displayed
      expect(find.text('50%'), findsOneWidget);
      
      // Verify progress indicator is present
      expect(find.byType(LinearProgressIndicator), findsOneWidget);
    });

    testWidgets('UploadProgressCard should show indeterminate progress when no progress value', (WidgetTester tester) async {
      await tester.pumpWidget(
        MaterialApp(
          home: Scaffold(
            body: UploadProgressCard(
              isUploading: true,
            ),
          ),
        ),
      );

      // Verify the upload text is displayed
      expect(find.text('Please wait for upload to complete...'), findsOneWidget);
      
      // Verify no percentage text is displayed
      expect(find.textContaining('%'), findsNothing);
      
      // Verify progress indicator is present (indeterminate)
      expect(find.byType(LinearProgressIndicator), findsOneWidget);
    });

    testWidgets('UploadProgressCard should display correct progress percentage', (WidgetTester tester) async {
      await tester.pumpWidget(
        MaterialApp(
          home: Scaffold(
            body: UploadProgressCard(
              progress: 0.75,
              isUploading: true,
            ),
          ),
        ),
      );

      // Verify the correct percentage is displayed
      expect(find.text('75%'), findsOneWidget);
    });
  });
}
