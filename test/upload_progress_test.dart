import 'package:flutter_test/flutter_test.dart';
import 'package:sluqe/services/upload_progress_service.dart';

void main() {
  group('Upload Progress System Tests', () {
    test('UploadProgressService should broadcast progress updates', () async {
      final progressService = UploadProgressService();
      final progressValues = <double>[];

      final subscription = progressService.progressStream.listen((progress) {
        progressValues.add(progress);
      });

      // Simulate progress updates
      progressService.updateProgress(0.0);
      progressService.updateProgress(0.5);
      progressService.updateProgress(1.0);

      // Wait for stream to process
      await Future.delayed(Duration(milliseconds: 100));

      expect(progressValues, [0.0, 0.5, 1.0]);

      await subscription.cancel();
      progressService.dispose();
    });

    test('UploadProgressService should broadcast uploading state', () async {
      final progressService = UploadProgressService();
      final uploadingStates = <bool>[];

      final subscription = progressService.uploadingStream.listen((isUploading) {
        uploadingStates.add(isUploading);
      });

      // Simulate uploading state changes
      progressService.setUploading(true);
      progressService.setUploading(false);

      // Wait for stream to process
      await Future.delayed(Duration(milliseconds: 100));

      expect(uploadingStates, [true, false]);

      await subscription.cancel();
      progressService.dispose();
    });
  });
}
